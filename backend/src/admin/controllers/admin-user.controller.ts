import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  Query,
  UseGuards 
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody 
} from '@nestjs/swagger';
import { User, Role } from '@prisma/client';

import { UserService } from '../../user/user.service';
import { UserProfile } from '../../user/user.interface';
import { ApiResponse as CustomApiResponse, PaginatedResponse } from '../../common/dto/response.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { SearchUserDto } from '../dto/search-user.dto';

@ApiTags('admin/users')
@Controller('admin/users')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.ADMIN)
@ApiBearerAuth('JWT-auth')
export class AdminUserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ 
    summary: 'Create new user',
    description: 'Create a new user (Admin only)' 
  })
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
  })
  async create(@Body() createUserDto: CreateUserDto): Promise<CustomApiResponse<UserProfile>> {
    const user = await this.userService.createUser(createUserDto);
    return {
      success: true,
      data: user,
      message: 'User created successfully',
    };
  }

  @Get()
  @ApiOperation({ 
    summary: 'Get all users with pagination and filtering',
    description: 'Retrieve all users with optional search, filtering, and pagination (Admin only)' 
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search by email, firstName, or lastName' })
  @ApiQuery({ name: 'role', required: false, enum: Role, description: 'Filter by user role' })
  async findAll(@Query() query: SearchUserDto): Promise<PaginatedResponse<UserProfile>> {
    const result = await this.userService.findAllUsers(query);
    return {
      success: true,
      data: result.users,
      pagination: result.pagination,
      message: 'Users retrieved successfully',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID (Admin)' })
  @ApiParam({ name: 'id', type: String, description: 'User ID' })
  async findOne(@Param('id') id: string): Promise<CustomApiResponse<UserProfile>> {
    const user = await this.userService.findOne(id);
    return {
      success: true,
      data: user,
      message: 'User retrieved successfully',
    };
  }

  @Patch(':id')
  @ApiOperation({ 
    summary: 'Update user',
    description: 'Update an existing user (Admin only)' 
  })
  @ApiParam({ name: 'id', type: String, description: 'User ID' })
  @ApiBody({ type: UpdateUserDto })
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<CustomApiResponse<UserProfile>> {
    const user = await this.userService.updateUser(id, updateUserDto);
    return {
      success: true,
      data: user,
      message: 'User updated successfully',
    };
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete user',
    description: 'Delete a user (Admin only)' 
  })
  @ApiParam({ name: 'id', type: String, description: 'User ID' })
  async remove(@Param('id') id: string): Promise<CustomApiResponse<null>> {
    await this.userService.deleteUser(id);
    return {
      success: true,
      data: null,
      message: 'User deleted successfully',
    };
  }

  @Patch(':id/role')
  @ApiOperation({ 
    summary: 'Update user role',
    description: 'Update user role (Admin only)' 
  })
  @ApiParam({ name: 'id', type: String, description: 'User ID' })
  @ApiBody({ 
    schema: {
      type: 'object',
      properties: {
        role: { type: 'string', enum: ['ADMIN', 'CUSTOMER'] }
      },
      required: ['role']
    } 
  })
  async updateRole(
    @Param('id') id: string,
    @Body('role') role: Role,
  ): Promise<CustomApiResponse<UserProfile>> {
    const user = await this.userService.updateUserRole(id, role);
    return {
      success: true,
      data: user,
      message: 'User role updated successfully',
    };
  }

  @Get('stats/overview')
  @ApiOperation({ 
    summary: 'Get user statistics',
    description: 'Get user statistics and overview (Admin only)' 
  })
  async getStats(): Promise<CustomApiResponse<any>> {
    const stats = await this.userService.getUserStats();
    return {
      success: true,
      data: stats,
      message: 'User statistics retrieved successfully',
    };
  }
}
