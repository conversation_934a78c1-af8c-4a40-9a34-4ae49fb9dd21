import { Cart, CartItem, Product } from '@prisma/client';
import { AddToCartDto } from './dto/add-to-cart.dto';
import { CartResponseDto } from './dto/cart-response.dto';
import { UpdateCartItemDto } from './dto/update-cart-item.dto';


export type CartWithItems = Cart & {
  items: (CartItem & {
    product: Product;
  })[];
};

export interface ICartService {
  getOrCreateCart(userId: string): Promise<Cart>;
  getCartWithItems(userId: string): Promise<CartResponseDto>;
  addToCart(userId: string, addToCartDto: AddToCartDto): Promise<CartResponseDto>;
  updateCartItem(userId: string, itemId: string, updateDto: UpdateCartItemDto): Promise<CartResponseDto>;
  removeFromCart(userId: string, itemId: string): Promise<CartResponseDto>;
  clearCart(userId: string): Promise<void>;
}